import React, { useState, useEffect } from 'react';
import { Send, MessageSquare, Mic, Image, Settings, Activity, Users, BarChart3, RefreshCw, AlertCircle, CheckCircle, Clock } from 'lucide-react';

const TelegramBotDashboard = () => {
  const [messages, setMessages] = useState([]);
  const [isConnected, setIsConnected] = useState(false);
  const [stats, setStats] = useState({
    totalMessages: 0,
    taskMessages: 0,
    otherMessages: 0,
    audioMessages: 0,
    imageMessages: 0,
    textMessages: 0
  });
  const [testMessage, setTestMessage] = useState('');
  const [testMessageType, setTestMessageType] = useState('text');
  const [isLoading, setIsLoading] = useState(false);
  const [webhookStatus, setWebhookStatus] = useState('unknown');

  const webhookUrl = "https://n8n.srv832954.hstgr.cloud/webhook-test/your-endpoint";

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate incoming messages
      if (Math.random() > 0.95) {
        const messageTypes = ['text', 'audio', 'image'];
        const messageType = messageTypes[Math.floor(Math.random() * messageTypes.length)];
        const isTask = Math.random() > 0.6;
        
        const newMessage = {
          id: Date.now(),
          type: messageType,
          category: isTask ? 'task' : 'other',
          content: isTask ? 'إنشاء مهمة جديدة' : 'رسالة عادية',
          timestamp: new Date(),
          user: 'Test User',
          status: 'processed'
        };

        setMessages(prev => [newMessage, ...prev].slice(0, 50));
        
        setStats(prev => ({
          totalMessages: prev.totalMessages + 1,
          taskMessages: prev.taskMessages + (isTask ? 1 : 0),
          otherMessages: prev.otherMessages + (!isTask ? 1 : 0),
          audioMessages: prev.audioMessages + (messageType === 'audio' ? 1 : 0),
          imageMessages: prev.imageMessages + (messageType === 'image' ? 1 : 0),
          textMessages: prev.textMessages + (messageType === 'text' ? 1 : 0)
        }));
      }
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  // Check webhook status
  const checkWebhookStatus = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(webhookUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      setWebhookStatus(response.ok ? 'active' : 'inactive');
      setIsConnected(response.ok);
    } catch (error) {
      console.error('Webhook check failed:', error);
      setWebhookStatus('error');
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Send test message to webhook
  const sendTestMessage = async () => {
    if (!testMessage.trim()) return;

    setIsLoading(true);
    try {
      const testPayload = {
        body: {
          message: {
            from: {
              id: 12345678999,
              first_name: "Test",
              last_name: "User"
            },
            chat: {
              id: 12345678999
            },
            ...(testMessageType === 'text' && { text: testMessage }),
            ...(testMessageType === 'audio' && { 
              voice: { file_id: "test_audio_file_id" },
              text: testMessage 
            }),
            ...(testMessageType === 'image' && { 
              photo: [{ file_id: "test_image_file_id" }],
              caption: testMessage 
            })
          }
        }
      };

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testPayload)
      });

      if (response.ok) {
        const newMessage = {
          id: Date.now(),
          type: testMessageType,
          category: 'test',
          content: testMessage,
          timestamp: new Date(),
          user: 'Test User',
          status: 'sent'
        };

        setMessages(prev => [newMessage, ...prev]);
        setTestMessage('');
        
        setStats(prev => ({
          ...prev,
          totalMessages: prev.totalMessages + 1,
          [testMessageType + 'Messages']: prev[testMessageType + 'Messages'] + 1
        }));
      }
    } catch (error) {
      console.error('Test message failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMessageIcon = (type) => {
    switch (type) {
      case 'audio': return <Mic className="w-4 h-4 text-blue-500" />;
      case 'image': return <Image className="w-4 h-4 text-green-500" />;
      default: return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  const getCategoryBadge = (category) => {
    const badges = {
      task: 'bg-orange-100 text-orange-800 border-orange-200',
      other: 'bg-blue-100 text-blue-800 border-blue-200',
      test: 'bg-purple-100 text-purple-800 border-purple-200'
    };
    
    return badges[category] || badges.other;
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'processed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'sent': return <Clock className="w-4 h-4 text-yellow-500" />;
      default: return <AlertCircle className="w-4 h-4 text-red-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Telegram Bot Dashboard
              </h1>
              <p className="text-gray-600">
                مراقبة ومتابعة رسائل البوت وتصنيف المهام
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full ${
                  webhookStatus === 'active' ? 'bg-green-500' : 
                  webhookStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                }`}></div>
                <span className="text-sm font-medium">
                  {webhookStatus === 'active' ? 'متصل' : 
                   webhookStatus === 'error' ? 'خطأ' : 'غير معروف'}
                </span>
              </div>
              
              <button
                onClick={checkWebhookStatus}
                disabled={isLoading}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span>فحص الحالة</span>
              </button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الرسائل</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalMessages}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المهام</p>
                <p className="text-2xl font-bold text-orange-600">{stats.taskMessages}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رسائل أخرى</p>
                <p className="text-2xl font-bold text-blue-600">{stats.otherMessages}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رسائل نصية</p>
                <p className="text-2xl font-bold text-gray-600">{stats.textMessages}</p>
              </div>
              <MessageSquare className="w-8 h-8 text-gray-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رسائل صوتية</p>
                <p className="text-2xl font-bold text-blue-600">{stats.audioMessages}</p>
              </div>
              <Mic className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الصور</p>
                <p className="text-2xl font-bold text-green-600">{stats.imageMessages}</p>
              </div>
              <Image className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Test Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Send className="w-5 h-5 mr-2" />
                اختبار الرسائل
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الرسالة
                  </label>
                  <select
                    value={testMessageType}
                    onChange={(e) => setTestMessageType(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="text">نص</option>
                    <option value="audio">صوت</option>
                    <option value="image">صورة</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    محتوى الرسالة
                  </label>
                  <textarea
                    value={testMessage}
                    onChange={(e) => setTestMessage(e.target.value)}
                    placeholder="اكتب رسالة تجريبية..."
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows="3"
                  />
                </div>

                <button
                  onClick={sendTestMessage}
                  disabled={isLoading || !testMessage.trim()}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                >
                  <Send className="w-4 h-4" />
                  <span>إرسال رسالة تجريبية</span>
                </button>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-900 mb-2">معلومات الـ Webhook</h4>
                <p className="text-xs text-gray-600 break-all">{webhookUrl}</p>
              </div>
            </div>
          </div>

          {/* Messages Feed */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                  <MessageSquare className="w-5 h-5 mr-2" />
                  آخر الرسائل
                </h3>
                <span className="text-sm text-gray-500">{messages.length} رسالة</span>
              </div>

              <div className="space-y-3 max-h-96 overflow-y-auto">
                {messages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد رسائل حتى الآن
                  </div>
                ) : (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                    >
                      <div className="flex-shrink-0 mt-1">
                        {getMessageIcon(message.type)}
                      </div>
                      
                      <div className="flex-grow min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-900">
                            {message.user}
                          </span>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getCategoryBadge(message.category)}`}>
                              {message.category === 'task' ? 'مهمة' : 
                               message.category === 'test' ? 'اختبار' : 'أخرى'}
                            </span>
                            {getStatusIcon(message.status)}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 truncate">
                          {message.content}
                        </p>
                        
                        <p className="text-xs text-gray-400 mt-1">
                          {message.timestamp.toLocaleTimeString('ar-EG')}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TelegramBotDashboard;